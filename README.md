# AI-Coding-Agent

An intelligent coding agent that bridges SWE-Agent capabilities with IDE plugins for Vim (and future VS Code support).

## Overview

This project creates a bridge between the powerful [SWE-Agent](https://github.com/SWE-agent/SWE-agent) AI coding assistant and IDE plugins. It extends SWE-Agent's capabilities while providing a seamless integration with your development environment.

### Features

#### Core Features
- Seamless integration with the SWE-Agent codebase (included as a Git submodule)
- Bridge API for connecting SWE-Agent with IDE plugins
- Vim plugin integration
- Future VS Code extension support

#### Phase 1 Enhanced Features
- **Multi-Session Management**: Run multiple concurrent SWE-Agent sessions
- **Real-time Progress Tracking**: Monitor agent execution progress in real-time
- **Enhanced Configuration**: Advanced configuration options with full SWE-Agent support
- **Session-based API**: New session-based endpoints for better control and monitoring
- **Backward Compatibility**: All existing functionality preserved during transition

#### Phase 2 Advanced Features
- **Advanced SWE-Agent Configuration**: Full feature parity with tool bundles, deployment options, model management
- **GitHub Repository Integration**: Automated repository cloning, setup, and management
- **Trajectory Management**: Complete action tracking with save/load/replay and analysis capabilities
- **Enhanced Retry Mechanisms**: Exponential backoff, circuit breaker patterns, intelligent error classification
- **Restructured Architecture**: Clean separation of core, API, integrations, and utilities

#### Phase 3.1 Vim Integration Features
- **Intelligent Code Completion**: Context-aware code suggestions powered by SWE-Agent
- **Advanced Code Analysis**: AST-based parsing with symbol extraction and scope analysis
- **Multi-Language Support**: Python, JavaScript/TypeScript with extensible architecture
- **RESTful & WebSocket APIs**: High-performance APIs for completion and analysis
- **Enhanced Vim Commands**: New `complete`, `analyze`, and `context` commands

#### Phase 3.2 Chat Integration Features
- **Multi-turn Conversations**: Persistent chat sessions with intelligent context preservation
- **Real-time Streaming**: Live response streaming with WebSocket and Server-Sent Events
- **Context-Aware Chat**: Automatic file, selection, and symbol context in conversations
- **Session Management**: Create, manage, and persist chat sessions across Vim sessions
- **Enhanced Chat Commands**: New `chat`, `chat_create`, `chat_list`, and `chat_history` commands

#### Phase 3.3 OAuth Authentication Features (NEW)
- **OAuth 2.0 Provider**: Complete OAuth 2.0 server with authorization code, client credentials, refresh token, and device flows
- **JWT Token Management**: Secure JWT tokens with validation, refresh, and revocation capabilities
- **Session Authentication**: User management with scope-based authorization and API key support
- **Secured API Endpoints**: Protected endpoints with optional and required authentication modes
- **Enterprise Security**: Industry-standard security with PKCE support and secure token handling

## Requirements

- **Python**: Version 3.11 or higher (required for SWE-Agent)
- **Git**: For cloning submodules
- **Vim**: Version 9.1 or higher (for Vim plugin)
- **Node.js**: Version 19 or higher (for Vim plugin)

## Project Structure

```
AI-Coding-Agent/
├── bridge/                  # Bridge component connecting SWE-Agent with IDE plugins
│   ├── __init__.py          # Package initialization
│   ├── __main__.py          # Main entry point
│   ├── core/                # Core session management and configuration
│   │   ├── __init__.py      # Core package initialization
│   │   ├── session_manager.py # Enhanced session management system
│   │   ├── enhanced_config.py # Advanced configuration system
│   │   └── config.py        # Legacy configuration management
│   ├── api/                 # API server and endpoint handlers
│   │   ├── __init__.py      # API package initialization
│   │   ├── enhanced_api_minimal.py # Enhanced API server with session support
│   │   └── api_server.py    # Legacy API server
│   ├── integrations/        # External system integrations
│   │   ├── __init__.py      # Integrations package initialization
│   │   ├── swe_agent_interface.py # Interface with SWE-Agent
│   │   ├── vim_integration.py # Vim plugin integration
│   │   ├── vim_client.py    # Client for Vim plugin
│   │   └── server_manager.py # SWE-Agent server management
│   └── utils/               # Utility modules
│       ├── __init__.py      # Utils package initialization
│       ├── env_loader.py    # Environment variable loader
│       └── python_check.py  # Python version checker
├── docs/                    # Documentation
│   ├── vim_integration.md   # Vim integration guide
│   ├── bridge_analysis_and_enhancement_plan.md # Enhancement analysis
│   └── phase1_migration_plan.md # Phase 1 migration guide
├── swe-agent/               # SWE-Agent submodule
├── swe-rex/                 # SWE-ReX submodule (required by SWE-Agent)
└── vim-extension/           # Vim plugin submodule
```

## Installation

### 1. Clone the Repository

Clone the repository with all submodules:

```bash
git clone --recurse-submodules https://github.com/ArunkumarKv-iN/AI-Coding-Agent.git
cd AI-Coding-Agent
```

If you've already cloned the repository without submodules, initialize them with:

```bash
git submodule update --init --recursive
```

### 2. Set Up Python Environment

Create a virtual environment using Python 3.11 or higher:

```bash
python3.11 -m venv swe_venv
source swe_venv/bin/activate
```

### 3. Install Dependencies

Install the required packages:

```bash
# Install SWE-ReX (required by SWE-Agent)
pip install -e ./swe-rex

# Install SWE-Agent
pip install -e ./swe-agent

# Install the bridge
pip install -e .
```

### 4. Configure API Keys

Create a `.env` file in the project root with your API keys:

```bash
cp .env.example .env
```

Then edit the `.env` file to add your API keys:

```
# Anthropic API Key (required for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI API Key (optional, for GPT models)
# OPENAI_API_KEY=your_openai_api_key_here

# SWE-Agent Configuration
SWE_AGENT_MODEL=claude-3-opus-20240229
SWE_AGENT_PORT=8000

# Bridge Configuration
BRIDGE_API_PORT=8080
BRIDGE_VIM_PORT=8081
```

## Usage

### Starting the Bridge

Activate the virtual environment and start the bridge:

```bash
source swe_venv/bin/activate
python -m bridge
```

This will start both the API server and Vim integration server.

Alternatively, use the provided convenience script:

```bash
./start_bridge.sh
```

#### Command Line Options

- Start only the API server: `python -m bridge --api-only`
- Start only the Vim integration: `python -m bridge --vim-only`
- Use a custom configuration file: `python -m bridge --config my_config.yaml`
- Specify custom ports: `python -m bridge --api-port 8080 --vim-port 8081`

#### Enhanced Mode (Phase 1)

To use the enhanced features with multi-session support:

```bash
# Start in enhanced mode
python -m bridge --enhanced

# Or set environment variable
export BRIDGE_ENABLE_ENHANCED_SESSIONS=true
python -m bridge
```

Enhanced mode provides:
- Multiple concurrent SWE-Agent sessions
- Real-time progress tracking
- Session-based API endpoints
- Advanced configuration options

### Using with Vim

1. Configure your `.vimrc` or `init.vim`:

```vim
" AI Coding Agent configuration
let g:ai_coding_agent_host = 'localhost'
let g:ai_coding_agent_port = 8081
let g:ai_coding_agent_python = 'python3'  " Path to Python executable
let g:ai_coding_agent_bridge_path = '/path/to/AI-Coding-Agent'  " Update this path
```

2. Use the Vim plugin commands:

```vim
" Run the AI Coding Agent on a task
:AICodingAgentRun "Create a function to calculate the Fibonacci sequence"

" Stop the current AI Coding Agent task
:AICodingAgentStop

" Check if the bridge is running
:AICodingAgentPing
```

See the [Vim Integration Guide](docs/vim_integration.md) for more details.

### Phase 3.1 Code Completion Features

The new code completion system provides intelligent suggestions and code analysis:

#### Using the Vim Client for Completion

```bash
# Get intelligent code completions
python -m bridge.integrations.vim_client complete \
  --file-path example.py \
  --content "def calculate_area(radius):\n    return math.pi * radius" \
  --cursor-line 1 \
  --cursor-column 25 \
  --language python

# Analyze file structure and symbols
python -m bridge.integrations.vim_client analyze \
  --file-path example.py \
  --content "$(cat example.py)" \
  --language python

# Get context information at cursor position
python -m bridge.integrations.vim_client context \
  --file-path example.py \
  --line 5 \
  --column 10
```

#### Using the REST API

```bash
# Code completion via REST API
curl -X POST http://localhost:8080/api/completion/complete \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "/path/to/file.py",
    "content": "def hello():\n    print(",
    "cursor_line": 1,
    "cursor_column": 10,
    "language": "python"
  }'

# File analysis via REST API
curl -X POST http://localhost:8080/api/completion/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "/path/to/file.py",
    "content": "def hello():\n    print(\"Hello\")",
    "language": "python"
  }'
```

### Phase 3.2 Multi-turn Chat Features

The new chat system provides conversational AI assistance with context preservation:

#### Using the Vim Client for Chat

```bash
# Create a new chat session
python -m bridge.integrations.vim_client chat_create \
  --title "Python Development Help" \
  --file-path example.py \
  --language python

# Send a chat message with context
python -m bridge.integrations.vim_client chat \
  --session-id session_123 \
  --message "How can I optimize this function for better performance?" \
  --file-path example.py \
  --content "$(cat example.py)" \
  --language python

# List all chat sessions
python -m bridge.integrations.vim_client chat_list

# Get chat conversation history
python -m bridge.integrations.vim_client chat_history \
  --session-id session_123
```

#### Using the Chat REST API

```bash
# Create a chat session
curl -X POST http://localhost:8080/api/chat/sessions \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Code Review Session",
    "context": {
      "file_path": "/path/to/file.py",
      "language": "python"
    }
  }'

# Send a message with streaming response
curl -X POST http://localhost:8080/api/chat/sessions/session_123/stream \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Can you help me refactor this code?",
    "context": {
      "file_path": "/path/to/file.py",
      "content": "def old_function():\n    pass",
      "selection": "def old_function():\n    pass"
    }
  }'

# Get session history
curl http://localhost:8080/api/chat/sessions/session_123
```

### Phase 3.3 OAuth Authentication Features

The new authentication system provides enterprise-grade security with OAuth 2.0:

#### OAuth 2.0 Authentication Flow

```bash
# 1. Get device code for CLI authentication
curl -X POST http://localhost:8080/auth/device \
  -d "client_id=ai-coding-agent-default&scope=read write"

# Response: {"device_code": "...", "user_code": "ABC12345", "verification_uri": "..."}

# 2. User visits verification URI and enters user code

# 3. Poll for token
curl -X POST http://localhost:8080/auth/token \
  -d "grant_type=urn:ietf:params:oauth:grant-type:device_code&device_code=...&client_id=ai-coding-agent-default"

# Response: {"access_token": "...", "refresh_token": "...", "expires_in": 3600}
```

#### Using Authenticated APIs

```bash
# Use access token for authenticated requests
curl -X POST http://localhost:8080/api/chat/sessions \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Authenticated Chat Session",
    "context": {"file_path": "/path/to/file.py"}
  }'

# Get user information
curl -X GET http://localhost:8080/auth/userinfo \
  -H "Authorization: Bearer your_access_token"

# List user sessions
curl -X GET http://localhost:8080/auth/sessions \
  -H "Authorization: Bearer your_access_token"
```

#### Client Credentials for Automation

```bash
# Get access token for automation/scripts
curl -X POST http://localhost:8080/auth/token \
  -d "grant_type=client_credentials&client_id=ai-coding-agent-default&client_secret=your_client_secret&scope=read write"
```

### Enhanced API Endpoints (Phase 1)

When running in enhanced mode, additional API endpoints are available:

#### Session Management
```bash
# Create a new session
POST /api/sessions
{
  "problem_statement": "Fix the authentication bug",
  "repo_path": "/path/to/repo",
  "model_name": "claude-3-opus-20240229"
}

# List all sessions
GET /api/sessions

# Get session details
GET /api/sessions/{session_id}

# Start a session
POST /api/sessions/{session_id}/start

# Stop a session
POST /api/sessions/{session_id}/stop

# Get session trajectory
GET /api/sessions/{session_id}/trajectory
```

#### Phase 3.1 Completion Endpoints
New code completion and analysis endpoints:
```bash
# Code completion
POST /api/completion/complete
{
  "file_path": "/path/to/file.py",
  "content": "file content",
  "cursor_line": 10,
  "cursor_column": 5,
  "language": "python"
}

# File analysis
POST /api/completion/analyze
{
  "file_path": "/path/to/file.py",
  "content": "file content",
  "language": "python"
}

# Context at position
POST /api/completion/context
{
  "file_path": "/path/to/file.py",
  "line": 10,
  "column": 5
}

# Completion service health
GET /api/completion/health
```

#### Phase 3.2 Chat Endpoints
New multi-turn chat and conversation endpoints:
```bash
# Create chat session
POST /api/chat/sessions
{
  "title": "Code Review Session",
  "context": {
    "file_path": "/path/to/file.py",
    "language": "python"
  }
}

# Send message with streaming response
POST /api/chat/sessions/{session_id}/stream
{
  "message": "How can I improve this code?",
  "context": {
    "file_path": "/path/to/file.py",
    "content": "file content",
    "selection": "selected code"
  }
}

# Send message (non-streaming)
POST /api/chat/sessions/{session_id}/messages
{
  "message": "Explain this function",
  "context": {...}
}

# Get session and history
GET /api/chat/sessions/{session_id}

# List all sessions
GET /api/chat/sessions

# Update session context
PUT /api/chat/sessions/{session_id}/context
{
  "file_path": "/path/to/file.py",
  "current_selection": "selected code"
}

# Get session summary
GET /api/chat/sessions/{session_id}/summary

# Delete session
DELETE /api/chat/sessions/{session_id}

# Clear session messages
POST /api/chat/sessions/{session_id}/clear

# Chat service health
GET /api/chat/health
```

#### Phase 3.3 Authentication Endpoints
OAuth 2.0 and JWT authentication endpoints:
```bash
# OAuth 2.0 Authorization (redirect-based)
GET /auth/authorize?response_type=code&client_id=...&redirect_uri=...&scope=read+write

# Token endpoint (multiple grant types)
POST /auth/token
{
  "grant_type": "authorization_code|client_credentials|refresh_token",
  "client_id": "client_id",
  "client_secret": "client_secret",
  "code": "auth_code",  # for authorization_code grant
  "refresh_token": "...",  # for refresh_token grant
  "scope": "read write"
}

# Device authorization flow
POST /auth/device
{
  "client_id": "ai-coding-agent-default",
  "scope": "read write"
}

# Device verification page
GET /auth/device?user_code=ABC12345

# User information (OpenID Connect)
GET /auth/userinfo
Authorization: Bearer access_token

# Token revocation
POST /auth/revoke
{
  "token": "token_to_revoke",
  "client_id": "client_id",
  "client_secret": "client_secret"
}

# User session management
GET /auth/sessions
Authorization: Bearer access_token

DELETE /auth/sessions/{token_id}
Authorization: Bearer access_token

# Authentication service health
GET /auth/health
```

#### Legacy Compatibility
The original endpoints continue to work:
```bash
POST /api/run    # Automatically creates a session
POST /api/stop   # Stops all running sessions
GET /health      # Health check
```

## Testing

To test the bridge and SWE-agent integration:

```bash
source swe_venv/bin/activate

# Test legacy functionality
python test_bridge.py  # Test the bridge API and Vim integration servers
python test_swe_agent.py  # Test the SWE-agent integration

# Test Phase 1 enhanced features
python test_phase1_working.py  # Test enhanced session management and API

# Test Phase 3.1 code completion features
python test_phase3_completion.py  # Test completion engine and context analysis
python simple_completion_test.py  # Simple completion functionality test

# Test Phase 3.2 multi-turn chat features
python test_phase3_2_chat.py  # Test chat manager and conversation context

# Test Phase 3.3 OAuth authentication features
python test_phase3_3_auth.py  # Test OAuth provider, JWT tokens, and auth endpoints
```

## Development

### Adding New Features

To add new features to the bridge:

1. Identify the missing capability in SWE-Agent
2. Implement the feature in the bridge component
3. Update the API server to expose the new feature
4. Update the IDE plugins to use the new feature

### Handling Submodule Changes

Since we don't have direct write access to the submodule repositories (swe-agent, swe-rex, vim-extension), we use a patch-based approach to manage changes:

#### Creating Patches

If you need to modify any of the submodules, make your changes and then create a patch:

```bash
python setup.py create_patches --submodule=swe-agent
```

This will create a patch file in the `patches/swe-agent/` directory. You can also create patches for other submodules:

```bash
python setup.py create_patches --submodule=swe-rex
python setup.py create_patches --submodule=vim-extension
```

Or create patches for all submodules at once:

```bash
python setup.py create_patches
```

#### Applying Patches

Patches are automatically applied during installation or development setup. You can also apply them manually:

```bash
python setup.py apply_patches
```

Or apply patches for a specific submodule:

```bash
python setup.py apply_patches --submodule=swe-agent
```

This approach allows us to track and manage changes to the submodules without needing write access to their repositories.

### Project Roadmap

#### Completed
- [x] Basic bridge implementation
- [x] Vim plugin integration
- [x] **Phase 1: Core Infrastructure Enhancement**
  - [x] Multi-session management system
  - [x] Enhanced configuration with full SWE-Agent support
  - [x] Session-based API endpoints
  - [x] Real-time progress tracking
  - [x] Backward compatibility maintenance
- [x] **Phase 2: SWE-Agent Feature Parity**
  - [x] Advanced configuration options (tool bundles, deployment)
  - [x] GitHub repository integration
  - [x] Trajectory management and replay
  - [x] Enhanced retry mechanisms with circuit breaker
  - [x] Restructured codebase architecture

#### In Progress / Planned
- [🚧] **Phase 3: Vim-Extension Integration** (IN PROGRESS)
  - [x] **Phase 3.1: Code completion bridge** ✅
    - [x] Intelligent context analysis and code completion
    - [x] Multi-language support (Python, JavaScript/TypeScript)
    - [x] RESTful and WebSocket APIs for completion
    - [x] Enhanced Vim integration with new commands
  - [x] **Phase 3.2: Multi-turn chat with context** ✅
    - [x] Conversational interface with context preservation
    - [x] Real-time streaming responses
    - [x] Session management and message history
    - [x] Enhanced Vim chat commands
  - [x] **Phase 3.3: OAuth authentication system** ✅
    - [x] Complete OAuth 2.0 provider with multiple grant types
    - [x] JWT token management with secure validation
    - [x] Session authentication and user management
    - [x] Secured API endpoints with backward compatibility
  - [ ] **Phase 3.4: Enhanced workspace features**
- [ ] **Phase 4: Advanced Features**
  - [ ] VS Code extension
  - [ ] Web UI for monitoring and management
  - [ ] Performance optimization and scalability
  - [ ] Analytics and monitoring dashboard

### Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Troubleshooting

### Common Issues

1. **Python Version**: SWE-Agent requires Python 3.11 or higher. Check your Python version with `python --version`.

2. **API Keys**: Make sure you've added your API keys to the `.env` file.

3. **Port Conflicts**: If you get port binding errors, check if another process is using the same port.

4. **Submodule Issues**: If you encounter errors related to missing modules, ensure all submodules are properly initialized with `git submodule update --init --recursive`.

### Logs

Check the logs for more detailed error information:

```bash
cat logs/bridge.log
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [SWE-Agent](https://github.com/SWE-agent/SWE-agent) - The core AI coding agent
- [SWE-ReX](https://github.com/SWE-agent/SWE-ReX) - The runtime execution framework for SWE-Agent
