# AI-Coding-Agent

An intelligent coding agent that bridges SWE-Agent capabilities with IDE plugins for Vim (and future VS Code support).

## Overview

This project creates a bridge between the powerful [SWE-Agent](https://github.com/SWE-agent/SWE-agent) AI coding assistant and IDE plugins. It extends SWE-Agent's capabilities while providing a seamless integration with your development environment.

### Features

#### Core Features
- Seamless integration with the SWE-Agent codebase (included as a Git submodule)
- Bridge API for connecting SWE-Agent with IDE plugins
- Vim plugin integration
- Future VS Code extension support

#### Phase 1 Enhanced Features
- **Multi-Session Management**: Run multiple concurrent SWE-Agent sessions
- **Real-time Progress Tracking**: Monitor agent execution progress in real-time
- **Enhanced Configuration**: Advanced configuration options with full SWE-Agent support
- **Session-based API**: New session-based endpoints for better control and monitoring
- **Backward Compatibility**: All existing functionality preserved during transition

#### Phase 2 Advanced Features (NEW)
- **Advanced SWE-Agent Configuration**: Full feature parity with tool bundles, deployment options, model management
- **GitHub Repository Integration**: Automated repository cloning, setup, and management
- **Trajectory Management**: Complete action tracking with save/load/replay and analysis capabilities
- **Enhanced Retry Mechanisms**: Exponential backoff, circuit breaker patterns, intelligent error classification
- **Restructured Architecture**: Clean separation of core, API, integrations, and utilities

## Requirements

- **Python**: Version 3.11 or higher (required for SWE-Agent)
- **Git**: For cloning submodules
- **Vim**: Version 9.1 or higher (for Vim plugin)
- **Node.js**: Version 19 or higher (for Vim plugin)

## Project Structure

```
AI-Coding-Agent/
├── bridge/                  # Bridge component connecting SWE-Agent with IDE plugins
│   ├── __init__.py          # Package initialization
│   ├── __main__.py          # Main entry point
│   ├── core/                # Core session management and configuration
│   │   ├── __init__.py      # Core package initialization
│   │   ├── session_manager.py # Enhanced session management system
│   │   ├── enhanced_config.py # Advanced configuration system
│   │   └── config.py        # Legacy configuration management
│   ├── api/                 # API server and endpoint handlers
│   │   ├── __init__.py      # API package initialization
│   │   ├── enhanced_api_minimal.py # Enhanced API server with session support
│   │   └── api_server.py    # Legacy API server
│   ├── integrations/        # External system integrations
│   │   ├── __init__.py      # Integrations package initialization
│   │   ├── swe_agent_interface.py # Interface with SWE-Agent
│   │   ├── vim_integration.py # Vim plugin integration
│   │   ├── vim_client.py    # Client for Vim plugin
│   │   └── server_manager.py # SWE-Agent server management
│   └── utils/               # Utility modules
│       ├── __init__.py      # Utils package initialization
│       ├── env_loader.py    # Environment variable loader
│       └── python_check.py  # Python version checker
├── docs/                    # Documentation
│   ├── vim_integration.md   # Vim integration guide
│   ├── bridge_analysis_and_enhancement_plan.md # Enhancement analysis
│   └── phase1_migration_plan.md # Phase 1 migration guide
├── swe-agent/               # SWE-Agent submodule
├── swe-rex/                 # SWE-ReX submodule (required by SWE-Agent)
└── vim-extension/           # Vim plugin submodule
```

## Installation

### 1. Clone the Repository

Clone the repository with all submodules:

```bash
git clone --recurse-submodules https://github.com/ArunkumarKv-iN/AI-Coding-Agent.git
cd AI-Coding-Agent
```

If you've already cloned the repository without submodules, initialize them with:

```bash
git submodule update --init --recursive
```

### 2. Set Up Python Environment

Create a virtual environment using Python 3.11 or higher:

```bash
python3.11 -m venv swe_venv
source swe_venv/bin/activate
```

### 3. Install Dependencies

Install the required packages:

```bash
# Install SWE-ReX (required by SWE-Agent)
pip install -e ./swe-rex

# Install SWE-Agent
pip install -e ./swe-agent

# Install the bridge
pip install -e .
```

### 4. Configure API Keys

Create a `.env` file in the project root with your API keys:

```bash
cp .env.example .env
```

Then edit the `.env` file to add your API keys:

```
# Anthropic API Key (required for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI API Key (optional, for GPT models)
# OPENAI_API_KEY=your_openai_api_key_here

# SWE-Agent Configuration
SWE_AGENT_MODEL=claude-3-opus-20240229
SWE_AGENT_PORT=8000

# Bridge Configuration
BRIDGE_API_PORT=8080
BRIDGE_VIM_PORT=8081
```

## Usage

### Starting the Bridge

Activate the virtual environment and start the bridge:

```bash
source swe_venv/bin/activate
python -m bridge
```

This will start both the API server and Vim integration server.

Alternatively, use the provided convenience script:

```bash
./start_bridge.sh
```

#### Command Line Options

- Start only the API server: `python -m bridge --api-only`
- Start only the Vim integration: `python -m bridge --vim-only`
- Use a custom configuration file: `python -m bridge --config my_config.yaml`
- Specify custom ports: `python -m bridge --api-port 8080 --vim-port 8081`

#### Enhanced Mode (Phase 1)

To use the enhanced features with multi-session support:

```bash
# Start in enhanced mode
python -m bridge --enhanced

# Or set environment variable
export BRIDGE_ENABLE_ENHANCED_SESSIONS=true
python -m bridge
```

Enhanced mode provides:
- Multiple concurrent SWE-Agent sessions
- Real-time progress tracking
- Session-based API endpoints
- Advanced configuration options

### Using with Vim

1. Configure your `.vimrc` or `init.vim`:

```vim
" AI Coding Agent configuration
let g:ai_coding_agent_host = 'localhost'
let g:ai_coding_agent_port = 8081
let g:ai_coding_agent_python = 'python3'  " Path to Python executable
let g:ai_coding_agent_bridge_path = '/path/to/AI-Coding-Agent'  " Update this path
```

2. Use the Vim plugin commands:

```vim
" Run the AI Coding Agent on a task
:AICodingAgentRun "Create a function to calculate the Fibonacci sequence"

" Stop the current AI Coding Agent task
:AICodingAgentStop

" Check if the bridge is running
:AICodingAgentPing
```

See the [Vim Integration Guide](docs/vim_integration.md) for more details.

### Enhanced API Endpoints (Phase 1)

When running in enhanced mode, additional API endpoints are available:

#### Session Management
```bash
# Create a new session
POST /api/sessions
{
  "problem_statement": "Fix the authentication bug",
  "repo_path": "/path/to/repo",
  "model_name": "claude-3-opus-20240229"
}

# List all sessions
GET /api/sessions

# Get session details
GET /api/sessions/{session_id}

# Start a session
POST /api/sessions/{session_id}/start

# Stop a session
POST /api/sessions/{session_id}/stop

# Get session trajectory
GET /api/sessions/{session_id}/trajectory
```

#### Legacy Compatibility
The original endpoints continue to work:
```bash
POST /api/run    # Automatically creates a session
POST /api/stop   # Stops all running sessions
GET /health      # Health check
```

## Testing

To test the bridge and SWE-agent integration:

```bash
source swe_venv/bin/activate

# Test legacy functionality
python test_bridge.py  # Test the bridge API and Vim integration servers
python test_swe_agent.py  # Test the SWE-agent integration

# Test Phase 1 enhanced features
python test_phase1_working.py  # Test enhanced session management and API
```

## Development

### Adding New Features

To add new features to the bridge:

1. Identify the missing capability in SWE-Agent
2. Implement the feature in the bridge component
3. Update the API server to expose the new feature
4. Update the IDE plugins to use the new feature

### Handling Submodule Changes

Since we don't have direct write access to the submodule repositories (swe-agent, swe-rex, vim-extension), we use a patch-based approach to manage changes:

#### Creating Patches

If you need to modify any of the submodules, make your changes and then create a patch:

```bash
python setup.py create_patches --submodule=swe-agent
```

This will create a patch file in the `patches/swe-agent/` directory. You can also create patches for other submodules:

```bash
python setup.py create_patches --submodule=swe-rex
python setup.py create_patches --submodule=vim-extension
```

Or create patches for all submodules at once:

```bash
python setup.py create_patches
```

#### Applying Patches

Patches are automatically applied during installation or development setup. You can also apply them manually:

```bash
python setup.py apply_patches
```

Or apply patches for a specific submodule:

```bash
python setup.py apply_patches --submodule=swe-agent
```

This approach allows us to track and manage changes to the submodules without needing write access to their repositories.

### Project Roadmap

#### Completed
- [x] Basic bridge implementation
- [x] Vim plugin integration
- [x] **Phase 1: Core Infrastructure Enhancement**
  - [x] Multi-session management system
  - [x] Enhanced configuration with full SWE-Agent support
  - [x] Session-based API endpoints
  - [x] Real-time progress tracking
  - [x] Backward compatibility maintenance
- [x] **Phase 2: SWE-Agent Feature Parity**
  - [x] Advanced configuration options (tool bundles, deployment)
  - [x] GitHub repository integration
  - [x] Trajectory management and replay
  - [x] Enhanced retry mechanisms with circuit breaker
  - [x] Restructured codebase architecture

#### In Progress / Planned
- [ ] **Phase 3: Vim-Extension Integration**
  - [ ] Code completion bridge
  - [ ] Multi-turn chat with context
  - [ ] OAuth authentication
  - [ ] Workspace features
- [ ] **Phase 4: Advanced Features**
  - [ ] VS Code extension
  - [ ] Web UI for monitoring and management
  - [ ] Performance optimization and scalability
  - [ ] Analytics and monitoring dashboard

### Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Troubleshooting

### Common Issues

1. **Python Version**: SWE-Agent requires Python 3.11 or higher. Check your Python version with `python --version`.

2. **API Keys**: Make sure you've added your API keys to the `.env` file.

3. **Port Conflicts**: If you get port binding errors, check if another process is using the same port.

4. **Submodule Issues**: If you encounter errors related to missing modules, ensure all submodules are properly initialized with `git submodule update --init --recursive`.

### Logs

Check the logs for more detailed error information:

```bash
cat logs/bridge.log
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [SWE-Agent](https://github.com/SWE-agent/SWE-agent) - The core AI coding agent
- [SWE-ReX](https://github.com/SWE-agent/SWE-ReX) - The runtime execution framework for SWE-Agent
