"""
Configuration module for the AI Coding Agent bridge.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

from bridge.utils.env_loader import get_env

# Base paths
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
SWE_AGENT_PATH = PROJECT_ROOT / "swe-agent"

# Default configuration
DEFAULT_CONFIG = {
    "swe_agent": {
        "api_port": int(get_env("SWE_AGENT_PORT", 8000)),
        "api_host": "localhost",
        "model": get_env("SWE_AGENT_MODEL", "claude-3-opus-20240229"),
    },
    "bridge": {
        "api_port": int(get_env("BRIDGE_API_PORT", 8080)),
        "api_host": "localhost",
        "vim_port": int(get_env("BRIDGE_VIM_PORT", 8081)),
    },
    "logging": {
        "level": get_env("LOG_LEVEL", "INFO"),
        "file": str(PROJECT_ROOT / "logs" / "bridge.log"),
    },
    "api_keys": {
        "anthropic": get_env("ANTHROPIC_API_KEY", ""),
        "openai": get_env("OPENAI_API_KEY", ""),
    }
}


class Config:
    """Configuration class for the AI Coding Agent bridge."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration.
        
        Args:
            config_path: Path to a custom configuration file.
        """
        self.config = DEFAULT_CONFIG.copy()
        if config_path:
            self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> None:
        """
        Load configuration from a file.
        
        Args:
            config_path: Path to the configuration file.
        """
        import yaml
        try:
            with open(config_path, 'r') as f:
                custom_config = yaml.safe_load(f)
                self._update_nested_dict(self.config, custom_config)
        except Exception as e:
            print(f"Error loading config from {config_path}: {e}")
    
    def _update_nested_dict(self, d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a nested dictionary with another dictionary.
        
        Args:
            d: Dictionary to update.
            u: Dictionary with updates.
            
        Returns:
            Updated dictionary.
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                d[k] = self._update_nested_dict(d[k], v)
            else:
                d[k] = v
        return d
    
    def get(self, *keys: str, default: Any = None) -> Any:
        """
        Get a configuration value.
        
        Args:
            *keys: Keys to navigate the nested dictionary.
            default: Default value if the key is not found.
            
        Returns:
            Configuration value.
        """
        current = self.config
        for key in keys:
            if key not in current:
                return default
            current = current[key]
        return current


# Global configuration instance
config = Config()
