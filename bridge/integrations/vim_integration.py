"""
Enhanced integration module for the Vim plugin.
Supports code completion, chat, and advanced workspace features.
"""

import asyncio
import json
import socket
import threading
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import asdict

from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.integrations.code_completion import completion_engine, CompletionContext
from bridge.core.context_manager import context_analyzer

logger = logging.getLogger(__name__)


class VimIntegration:
    """Enhanced integration with the Vim plugin."""

    def __init__(self):
        """Initialize the Vim integration."""
        self.swe_agent = SWEAgentInterface()
        self.socket = None
        self.server_thread = None
        self.running = False
        self.active_sessions: Dict[str, Any] = {}
        self.completion_cache: Dict[str, Any] = {}
    
    def start_server(self, host: str = "localhost", port: int = 8081):
        """
        Start the server for Vim plugin communication.
        
        Args:
            host: Host to bind to.
            port: Port to bind to.
        """
        if self.running:
            logger.warning("Server is already running")
            return
        
        self.running = True
        self.server_thread = threading.Thread(
            target=self._run_server,
            args=(host, port),
            daemon=True
        )
        self.server_thread.start()
        logger.info(f"Vim integration server started on {host}:{port}")
    
    def stop_server(self):
        """Stop the server."""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except Exception as e:
                logger.error(f"Error closing socket: {e}")
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=1)
        
        logger.info("Vim integration server stopped")
    
    def _run_server(self, host: str, port: int):
        """
        Run the server.
        
        Args:
            host: Host to bind to.
            port: Port to bind to.
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((host, port))
            self.socket.listen(5)
            
            while self.running:
                try:
                    client_socket, address = self.socket.accept()
                    logger.info(f"Connection from {address}")
                    
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        logger.error(f"Error accepting connection: {e}")
        
        except Exception as e:
            logger.error(f"Error starting server: {e}")
        
        finally:
            if self.socket:
                self.socket.close()
    
    def _handle_client(self, client_socket: socket.socket):
        """
        Handle a client connection.
        
        Args:
            client_socket: Client socket.
        """
        try:
            # Set a timeout to avoid blocking forever
            client_socket.settimeout(60)
            
            # Receive data
            data = b""
            while True:
                chunk = client_socket.recv(4096)
                if not chunk:
                    break
                data += chunk
                
                # Check if we have received a complete message
                if data.endswith(b"\n"):
                    break
            
            if not data:
                logger.warning("No data received from client")
                return
            
            # Parse the request
            try:
                request = json.loads(data.decode("utf-8"))
                response = self._handle_request(request)
                
                # Send the response
                client_socket.sendall(json.dumps(response).encode("utf-8"))
            
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received: {data.decode('utf-8')}")
                client_socket.sendall(json.dumps({
                    "status": "error",
                    "message": "Invalid JSON"
                }).encode("utf-8"))
        
        except Exception as e:
            logger.error(f"Error handling client: {e}")
        
        finally:
            client_socket.close()
    
    def _handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle a request from the Vim plugin.

        Args:
            request: Request from the Vim plugin.

        Returns:
            Response to send back to the Vim plugin.
        """
        command = request.get("command")
        if not command:
            return {"status": "error", "message": "No command specified"}

        try:
            if command == "run":
                return self._handle_run_command(request)
            elif command == "stop":
                return self._handle_stop_command(request)
            elif command == "ping":
                return {"status": "success", "message": "pong"}
            elif command == "complete":
                return self._handle_completion_command(request)
            elif command == "analyze":
                return self._handle_analyze_command(request)
            elif command == "context":
                return self._handle_context_command(request)
            elif command == "chat":
                return self._handle_chat_command(request)
            else:
                return {"status": "error", "message": f"Unknown command: {command}"}
        except Exception as e:
            logger.error(f"Error handling command {command}: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_run_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle the run command."""
        problem_statement = request.get("problem_statement")
        if not problem_statement:
            return {"status": "error", "message": "No problem statement provided"}

        repo_path = request.get("repo_path")
        if not repo_path:
            return {"status": "error", "message": "No repository path provided"}

        model_name = request.get("model_name", "gpt-4")

        result = self.swe_agent.run_agent(
            problem_statement=problem_statement,
            repo_path=repo_path,
            model_name=model_name
        )

        return {"status": result}

    def _handle_stop_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle the stop command."""
        result = self.swe_agent.stop_agent()
        return {"status": "success" if result else "error"}

    def _handle_completion_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle code completion request."""
        try:
            # Validate required fields
            required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            # Create completion context
            context = CompletionContext(
                file_path=request['file_path'],
                content=request['content'],
                cursor_line=request['cursor_line'],
                cursor_column=request['cursor_column'],
                language=request.get('language', 'text'),
                project_root=request.get('project_root'),
                selection=request.get('selection')
            )

            # Get completions synchronously for Vim
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(completion_engine.get_completions(context))
                return {
                    "status": "success",
                    "completions": asdict(response)
                }
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Error in completion: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_analyze_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file analysis request."""
        try:
            # Validate required fields
            required_fields = ['file_path', 'content']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            # Analyze file
            file_context = context_analyzer.analyze_file(
                file_path=request['file_path'],
                content=request['content'],
                language=request.get('language')
            )

            return {
                "status": "success",
                "analysis": {
                    "file_path": file_context.file_path,
                    "language": file_context.language,
                    "symbols": [asdict(symbol) for symbol in file_context.symbols],
                    "imports": file_context.imports,
                    "dependencies": file_context.dependencies
                }
            }

        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_context_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context request."""
        try:
            # Validate required fields
            required_fields = ['file_path', 'line', 'column']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            # Get context at position
            symbol = context_analyzer.get_context_at_position(
                file_path=request['file_path'],
                line=request['line'],
                column=request['column']
            )

            return {
                "status": "success",
                "context": asdict(symbol) if symbol else None
            }

        except Exception as e:
            logger.error(f"Error getting context: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_chat_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle chat request (placeholder for Phase 3.2)."""
        return {
            "status": "success",
            "message": "Chat functionality will be implemented in Phase 3.2"
        }


# Singleton instance
vim_integration = VimIntegration()
