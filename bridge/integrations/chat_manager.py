"""
Multi-turn chat manager for conversational interface with context preservation.
Integrates with <PERSON><PERSON>E-Agent for code-related queries and maintains conversation history.
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import threading

from bridge.core.session_manager import session_manager, SessionConfig
from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.core.context_manager import context_analyzer

logger = logging.getLogger(__name__)


class MessageRole(Enum):
    """Message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatStatus(Enum):
    """Chat session status."""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class ChatMessage:
    """A single message in a chat conversation."""
    id: str
    role: MessageRole
    content: str
    timestamp: datetime
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        data = asdict(self)
        data['role'] = self.role.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ChatContext:
    """Context information for a chat session."""
    file_path: Optional[str] = None
    project_root: Optional[str] = None
    current_selection: Optional[str] = None
    cursor_position: Optional[Dict[str, int]] = None
    language: Optional[str] = None
    symbols_in_scope: Optional[List[Dict[str, Any]]] = None
    recent_changes: Optional[List[Dict[str, Any]]] = None


@dataclass
class ChatSession:
    """A chat conversation session."""
    session_id: str
    title: str
    messages: List[ChatMessage]
    context: ChatContext
    status: ChatStatus
    created_at: datetime
    updated_at: datetime
    swe_session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['messages'] = [msg.to_dict() for msg in self.messages]
        return data


class ChatManager:
    """Manages multi-turn chat conversations with context preservation."""
    
    def __init__(self):
        self.sessions: Dict[str, ChatSession] = {}
        self.swe_agent = SWEAgentInterface()
        self.lock = threading.RLock()
        self.active_streams: Dict[str, bool] = {}
    
    def create_session(self, title: str = None, context: ChatContext = None) -> str:
        """
        Create a new chat session.
        
        Args:
            title: Optional title for the session.
            context: Initial context for the session.
            
        Returns:
            Session ID.
        """
        session_id = str(uuid.uuid4())
        
        if title is None:
            title = f"Chat Session {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        if context is None:
            context = ChatContext()
        
        with self.lock:
            session = ChatSession(
                session_id=session_id,
                title=title,
                messages=[],
                context=context,
                status=ChatStatus.ACTIVE,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            self.sessions[session_id] = session
        
        logger.info(f"Created chat session {session_id}: {title}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a chat session by ID."""
        with self.lock:
            return self.sessions.get(session_id)
    
    def list_sessions(self, status: Optional[ChatStatus] = None) -> List[ChatSession]:
        """List all chat sessions, optionally filtered by status."""
        with self.lock:
            sessions = list(self.sessions.values())
        
        if status:
            sessions = [s for s in sessions if s.status == status]
        
        return sorted(sessions, key=lambda s: s.updated_at, reverse=True)
    
    def add_message(self, session_id: str, role: MessageRole, content: str, 
                   context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Add a message to a chat session.
        
        Args:
            session_id: Chat session ID.
            role: Message role (user, assistant, system).
            content: Message content.
            context: Optional context information.
            
        Returns:
            Message ID if successful, None otherwise.
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return None
            
            message_id = str(uuid.uuid4())
            message = ChatMessage(
                id=message_id,
                role=role,
                content=content,
                timestamp=datetime.now(),
                context=context
            )
            
            session.messages.append(message)
            session.updated_at = datetime.now()
        
        logger.debug(f"Added {role.value} message to session {session_id}")
        return message_id
    
    def update_context(self, session_id: str, context: ChatContext) -> bool:
        """Update the context for a chat session."""
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            session.context = context
            session.updated_at = datetime.now()
        
        return True
    
    async def send_message(self, session_id: str, message: str, 
                          context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """
        Send a message and get streaming response.
        
        Args:
            session_id: Chat session ID.
            message: User message.
            context: Optional context information.
            
        Yields:
            Response chunks as they are generated.
        """
        session = self.get_session(session_id)
        if not session:
            yield json.dumps({"error": "Session not found"})
            return
        
        # Add user message
        self.add_message(session_id, MessageRole.USER, message, context)
        
        try:
            # Update context if provided
            if context:
                await self._update_session_context(session_id, context)
            
            # Generate response using SWE-Agent
            response_chunks = []
            async for chunk in self._generate_response(session_id, message):
                response_chunks.append(chunk)
                yield chunk
            
            # Add assistant response
            full_response = "".join(response_chunks)
            self.add_message(session_id, MessageRole.ASSISTANT, full_response)
            
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            logger.error(error_msg)
            self.add_message(session_id, MessageRole.ASSISTANT, error_msg)
            yield json.dumps({"error": error_msg})
    
    async def _update_session_context(self, session_id: str, context: Dict[str, Any]):
        """Update session context with current file and cursor information."""
        session = self.get_session(session_id)
        if not session:
            return
        
        # Extract context information
        file_path = context.get('file_path')
        cursor_line = context.get('cursor_line')
        cursor_column = context.get('cursor_column')
        selection = context.get('selection')
        
        # Analyze current file if provided
        if file_path and context.get('content'):
            try:
                file_context = context_analyzer.analyze_file(
                    file_path, 
                    context['content'], 
                    context.get('language')
                )
                
                # Get symbols in scope
                symbols = []
                if cursor_line is not None:
                    symbols = context_analyzer.get_symbols_in_scope(file_path, cursor_line)
                
                # Update session context
                chat_context = ChatContext(
                    file_path=file_path,
                    project_root=context.get('project_root'),
                    current_selection=selection,
                    cursor_position={'line': cursor_line, 'column': cursor_column} if cursor_line is not None else None,
                    language=file_context.language,
                    symbols_in_scope=[asdict(symbol) for symbol in symbols]
                )
                
                self.update_context(session_id, chat_context)
                
            except Exception as e:
                logger.error(f"Error updating session context: {e}")
    
    async def _generate_response(self, session_id: str, message: str) -> AsyncGenerator[str, None]:
        """Generate response using SWE-Agent with conversation context."""
        session = self.get_session(session_id)
        if not session:
            return
        
        try:
            # Build context-aware prompt
            prompt = self._build_contextual_prompt(session, message)
            
            # Create or reuse SWE-Agent session
            if not session.swe_session_id:
                session_config = SessionConfig(
                    model_name="claude-3-haiku-20240307",  # Use faster model for chat
                    repo_path=session.context.project_root or ".",
                    problem_statement=prompt
                )
                session.swe_session_id = session_manager.create_session(session_config)
            
            # Stream response from SWE-Agent
            # For now, simulate streaming response
            response_text = await self._query_swe_agent(prompt, session.context)
            
            # Simulate streaming by yielding chunks
            words = response_text.split()
            for i, word in enumerate(words):
                chunk = word + (" " if i < len(words) - 1 else "")
                yield chunk
                await asyncio.sleep(0.05)  # Simulate streaming delay
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    def _build_contextual_prompt(self, session: ChatSession, message: str) -> str:
        """Build a context-aware prompt for SWE-Agent."""
        prompt_parts = []
        
        # Add conversation history
        if session.messages:
            prompt_parts.append("Previous conversation:")
            for msg in session.messages[-5:]:  # Last 5 messages for context
                role_prefix = "User" if msg.role == MessageRole.USER else "Assistant"
                prompt_parts.append(f"{role_prefix}: {msg.content}")
            prompt_parts.append("")
        
        # Add current context
        if session.context.file_path:
            prompt_parts.append(f"Current file: {session.context.file_path}")
        
        if session.context.language:
            prompt_parts.append(f"Language: {session.context.language}")
        
        if session.context.current_selection:
            prompt_parts.append(f"Selected code:\n```\n{session.context.current_selection}\n```")
        
        if session.context.symbols_in_scope:
            symbols = [s.get('name', '') for s in session.context.symbols_in_scope[:10]]
            prompt_parts.append(f"Available symbols: {', '.join(symbols)}")
        
        # Add current message
        prompt_parts.append(f"Current question: {message}")
        
        # Add instructions
        prompt_parts.append("""
Please provide a helpful response considering:
1. The conversation history and context
2. The current file and code selection
3. Available symbols and functions in scope
4. Best practices for the programming language

Be concise but thorough, and provide code examples when helpful.
""")
        
        return "\n".join(prompt_parts)
    
    async def _query_swe_agent(self, prompt: str, context: ChatContext) -> str:
        """Query SWE-Agent for chat response."""
        try:
            # This would integrate with the actual SWE-Agent execution
            # For now, return a simulated response
            await asyncio.sleep(0.5)  # Simulate processing time
            
            if "error" in prompt.lower():
                return "I can help you debug that error. Could you share the specific error message and the relevant code?"
            elif "function" in prompt.lower():
                return "I can help you with that function. Based on the context, here's what I suggest..."
            elif "refactor" in prompt.lower():
                return "For refactoring this code, I recommend the following approach..."
            else:
                return "I understand your question. Let me help you with that based on the current context and code."
                
        except Exception as e:
            logger.error(f"Error querying SWE-Agent: {e}")
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a chat session."""
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.info(f"Deleted chat session {session_id}")
                return True
        return False
    
    def clear_session_messages(self, session_id: str) -> bool:
        """Clear all messages from a chat session."""
        with self.lock:
            session = self.sessions.get(session_id)
            if session:
                session.messages.clear()
                session.updated_at = datetime.now()
                return True
        return False
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a chat session."""
        session = self.get_session(session_id)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "title": session.title,
            "status": session.status.value,
            "message_count": len(session.messages),
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "context": asdict(session.context) if session.context else None
        }


# Global chat manager instance
chat_manager = ChatManager()
