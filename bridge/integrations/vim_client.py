"""
Client for communicating with the Vim plugin.
"""

import json
import socket
import argparse
import sys
from typing import Dict, Any, Optional


def send_command(
    command: str,
    host: str = "localhost",
    port: int = 8081,
    **kwargs
) -> Dict[str, Any]:
    """
    Send a command to the bridge server.
    
    Args:
        command: Command to send.
        host: Host to connect to.
        port: Port to connect to.
        **kwargs: Additional arguments to include in the request.
        
    Returns:
        Response from the server.
    """
    # Create the request
    request = {"command": command, **kwargs}
    
    # Connect to the server
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(10)  # 10 second timeout
            sock.connect((host, port))
            
            # Send the request
            sock.sendall(json.dumps(request).encode("utf-8") + b"\n")
            
            # Receive the response
            data = b""
            while True:
                chunk = sock.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            # Parse the response
            if data:
                return json.loads(data.decode("utf-8"))
            else:
                return {"status": "error", "message": "No response from server"}
    
    except socket.timeout:
        return {"status": "error", "message": "Connection timed out"}
    except ConnectionRefusedError:
        return {"status": "error", "message": "Connection refused"}
    except Exception as e:
        return {"status": "error", "message": str(e)}


def main():
    """Main entry point for the Vim client."""
    parser = argparse.ArgumentParser(description="AI Coding Agent Vim Client")
    parser.add_argument(
        "command",
        choices=["run", "stop", "ping", "complete", "analyze", "context", "chat", "chat_create", "chat_list", "chat_history"],
        help="Command to send to the server"
    )
    parser.add_argument(
        "--host",
        default="localhost",
        help="Host to connect to"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8081,
        help="Port to connect to"
    )
    parser.add_argument(
        "--problem-statement",
        help="Problem statement for the 'run' command"
    )
    parser.add_argument(
        "--repo-path",
        help="Repository path for the 'run' command"
    )
    parser.add_argument(
        "--model-name",
        default="gpt-4",
        help="Model name for the 'run' command"
    )
    parser.add_argument(
        "--file-path",
        help="File path for completion and analysis commands"
    )
    parser.add_argument(
        "--content",
        help="File content for completion and analysis commands"
    )
    parser.add_argument(
        "--cursor-line",
        type=int,
        help="Cursor line for completion commands"
    )
    parser.add_argument(
        "--cursor-column",
        type=int,
        help="Cursor column for completion commands"
    )
    parser.add_argument(
        "--language",
        help="Programming language for analysis"
    )
    parser.add_argument(
        "--line",
        type=int,
        help="Line number for context commands"
    )
    parser.add_argument(
        "--column",
        type=int,
        help="Column number for context commands"
    )
    parser.add_argument(
        "--session-id",
        help="Chat session ID for chat commands"
    )
    parser.add_argument(
        "--message",
        help="Message content for chat commands"
    )
    parser.add_argument(
        "--title",
        help="Title for chat session creation"
    )

    args = parser.parse_args()
    
    # Build the kwargs based on the command
    kwargs = {}
    if args.command == "run":
        if not args.problem_statement:
            print("Error: --problem-statement is required for the 'run' command")
            sys.exit(1)
        if not args.repo_path:
            print("Error: --repo-path is required for the 'run' command")
            sys.exit(1)

        kwargs = {
            "problem_statement": args.problem_statement,
            "repo_path": args.repo_path,
            "model_name": args.model_name
        }

    elif args.command == "complete":
        if not args.file_path:
            print("Error: --file-path is required for the 'complete' command")
            sys.exit(1)
        if not args.content:
            print("Error: --content is required for the 'complete' command")
            sys.exit(1)
        if args.cursor_line is None:
            print("Error: --cursor-line is required for the 'complete' command")
            sys.exit(1)
        if args.cursor_column is None:
            print("Error: --cursor-column is required for the 'complete' command")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "content": args.content,
            "cursor_line": args.cursor_line,
            "cursor_column": args.cursor_column,
            "language": args.language
        }

    elif args.command == "analyze":
        if not args.file_path:
            print("Error: --file-path is required for the 'analyze' command")
            sys.exit(1)
        if not args.content:
            print("Error: --content is required for the 'analyze' command")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "content": args.content,
            "language": args.language
        }

    elif args.command == "context":
        if not args.file_path:
            print("Error: --file-path is required for the 'context' command")
            sys.exit(1)
        if args.line is None:
            print("Error: --line is required for the 'context' command")
            sys.exit(1)
        if args.column is None:
            print("Error: --column is required for the 'context' command")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "line": args.line,
            "column": args.column
        }

    elif args.command == "chat":
        if not args.session_id:
            print("Error: --session-id is required for the 'chat' command")
            sys.exit(1)
        if not args.message:
            print("Error: --message is required for the 'chat' command")
            sys.exit(1)

        kwargs = {
            "session_id": args.session_id,
            "message": args.message,
            "context": {
                "file_path": args.file_path,
                "content": args.content,
                "language": args.language
            }
        }

    elif args.command == "chat_create":
        kwargs = {
            "title": args.title,
            "context": {
                "file_path": args.file_path,
                "language": args.language
            }
        }

    elif args.command == "chat_history":
        if not args.session_id:
            print("Error: --session-id is required for the 'chat_history' command")
            sys.exit(1)

        kwargs = {
            "session_id": args.session_id
        }
    
    # Send the command
    response = send_command(
        command=args.command,
        host=args.host,
        port=args.port,
        **kwargs
    )
    
    # Print the response
    print(json.dumps(response, indent=2))


if __name__ == "__main__":
    main()
