"""
JWT token management for secure authentication and authorization.
Handles token creation, validation, refresh, and revocation.
"""

import base64
import hashlib
import hmac
import json
import logging
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import threading

logger = logging.getLogger(__name__)


class TokenType(Enum):
    """JWT token types."""
    ACCESS = "access"
    REFRESH = "refresh"
    ID = "id"


@dataclass
class TokenClaims:
    """JWT token claims."""
    iss: str  # Issuer
    sub: str  # Subject (user ID)
    aud: str  # Audience (client ID)
    exp: int  # Expiration time
    iat: int  # Issued at
    jti: str  # JWT ID
    typ: str  # Token type
    scope: List[str]  # Scopes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class JWTHeader:
    """JWT header."""
    alg: str = "HS256"
    typ: str = "JWT"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class TokenInfo:
    """Token information for tracking."""
    token_id: str
    user_id: str
    client_id: str
    token_type: TokenType
    scope: List[str]
    created_at: datetime
    expires_at: datetime
    is_revoked: bool = False
    last_used: Optional[datetime] = None


class TokenManager:
    """JWT token manager for authentication and authorization."""
    
    def __init__(self, secret_key: str = None, issuer: str = "ai-coding-agent"):
        self.secret_key = secret_key or self._generate_secret_key()
        self.issuer = issuer
        self.tokens: Dict[str, TokenInfo] = {}
        self.lock = threading.RLock()
        
        # Token lifetimes
        self.access_token_lifetime = timedelta(hours=1)
        self.refresh_token_lifetime = timedelta(days=30)
        self.id_token_lifetime = timedelta(hours=1)
        
        logger.info("JWT Token Manager initialized")
    
    def create_access_token(self, user_id: str, client_id: str, scope: List[str]) -> str:
        """
        Create JWT access token.
        
        Args:
            user_id: User identifier.
            client_id: OAuth client ID.
            scope: Token scopes.
            
        Returns:
            JWT access token.
        """
        return self._create_token(user_id, client_id, scope, TokenType.ACCESS, self.access_token_lifetime)
    
    def create_refresh_token(self, user_id: str, client_id: str, scope: List[str]) -> str:
        """
        Create JWT refresh token.
        
        Args:
            user_id: User identifier.
            client_id: OAuth client ID.
            scope: Token scopes.
            
        Returns:
            JWT refresh token.
        """
        return self._create_token(user_id, client_id, scope, TokenType.REFRESH, self.refresh_token_lifetime)
    
    def create_id_token(self, user_id: str, client_id: str, user_info: Dict[str, Any] = None) -> str:
        """
        Create JWT ID token.
        
        Args:
            user_id: User identifier.
            client_id: OAuth client ID.
            user_info: Additional user information.
            
        Returns:
            JWT ID token.
        """
        token_id = self._generate_token_id()
        now = datetime.now()
        expires_at = now + self.id_token_lifetime
        
        # Create claims
        claims = TokenClaims(
            iss=self.issuer,
            sub=user_id,
            aud=client_id,
            exp=int(expires_at.timestamp()),
            iat=int(now.timestamp()),
            jti=token_id,
            typ=TokenType.ID.value,
            scope=["openid"]
        )
        
        # Add user info to claims
        claims_dict = claims.to_dict()
        if user_info:
            claims_dict.update(user_info)
        
        # Create JWT
        token = self._encode_jwt(claims_dict)
        
        # Store token info
        with self.lock:
            self.tokens[token_id] = TokenInfo(
                token_id=token_id,
                user_id=user_id,
                client_id=client_id,
                token_type=TokenType.ID,
                scope=["openid"],
                created_at=now,
                expires_at=expires_at
            )
        
        logger.info(f"Created ID token for user {user_id}, client {client_id}")
        return token
    
    def validate_token(self, token: str, required_scope: List[str] = None) -> Optional[TokenClaims]:
        """
        Validate JWT token.
        
        Args:
            token: JWT token to validate.
            required_scope: Required scopes for authorization.
            
        Returns:
            Token claims if valid, None otherwise.
        """
        try:
            # Decode and verify JWT
            claims_dict = self._decode_jwt(token)
            claims = TokenClaims(**claims_dict)
            
            # Check expiration
            if datetime.now().timestamp() > claims.exp:
                logger.warning(f"Token expired: {claims.jti}")
                return None
            
            # Check if token is revoked
            with self.lock:
                token_info = self.tokens.get(claims.jti)
                if token_info and token_info.is_revoked:
                    logger.warning(f"Token revoked: {claims.jti}")
                    return None
                
                # Update last used time
                if token_info:
                    token_info.last_used = datetime.now()
            
            # Check required scope
            if required_scope:
                if not all(scope in claims.scope for scope in required_scope):
                    logger.warning(f"Insufficient scope: required {required_scope}, got {claims.scope}")
                    return None
            
            logger.debug(f"Token validated: {claims.jti}")
            return claims
            
        except Exception as e:
            logger.error(f"Token validation failed: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Tuple[str, str]]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token.
            
        Returns:
            Tuple of (new_access_token, new_refresh_token) if successful.
        """
        try:
            # Validate refresh token
            claims = self.validate_token(refresh_token)
            if not claims or claims.typ != TokenType.REFRESH.value:
                return None
            
            # Create new tokens
            new_access_token = self.create_access_token(claims.sub, claims.aud, claims.scope)
            new_refresh_token = self.create_refresh_token(claims.sub, claims.aud, claims.scope)
            
            # Revoke old refresh token
            self.revoke_token(claims.jti)
            
            logger.info(f"Refreshed tokens for user {claims.sub}, client {claims.aud}")
            return new_access_token, new_refresh_token
            
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return None
    
    def revoke_token(self, token_id: str) -> bool:
        """
        Revoke token by ID.
        
        Args:
            token_id: Token ID to revoke.
            
        Returns:
            True if token was revoked.
        """
        with self.lock:
            token_info = self.tokens.get(token_id)
            if token_info:
                token_info.is_revoked = True
                logger.info(f"Revoked token: {token_id}")
                return True
        return False
    
    def revoke_user_tokens(self, user_id: str, client_id: str = None) -> int:
        """
        Revoke all tokens for a user.
        
        Args:
            user_id: User identifier.
            client_id: Optional client ID filter.
            
        Returns:
            Number of tokens revoked.
        """
        revoked_count = 0
        with self.lock:
            for token_info in self.tokens.values():
                if (token_info.user_id == user_id and 
                    (client_id is None or token_info.client_id == client_id) and
                    not token_info.is_revoked):
                    token_info.is_revoked = True
                    revoked_count += 1
        
        logger.info(f"Revoked {revoked_count} tokens for user {user_id}")
        return revoked_count
    
    def get_token_info(self, token_id: str) -> Optional[TokenInfo]:
        """Get token information by ID."""
        return self.tokens.get(token_id)
    
    def list_user_tokens(self, user_id: str, active_only: bool = True) -> List[TokenInfo]:
        """
        List tokens for a user.
        
        Args:
            user_id: User identifier.
            active_only: Only return non-revoked tokens.
            
        Returns:
            List of token information.
        """
        tokens = []
        with self.lock:
            for token_info in self.tokens.values():
                if (token_info.user_id == user_id and
                    (not active_only or not token_info.is_revoked)):
                    tokens.append(token_info)
        
        return sorted(tokens, key=lambda t: t.created_at, reverse=True)
    
    def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens.
        
        Returns:
            Number of tokens cleaned up.
        """
        now = datetime.now()
        cleaned_count = 0
        
        with self.lock:
            expired_tokens = []
            for token_id, token_info in self.tokens.items():
                if now > token_info.expires_at:
                    expired_tokens.append(token_id)
            
            for token_id in expired_tokens:
                del self.tokens[token_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} expired tokens")
        
        return cleaned_count
    
    def get_token_stats(self) -> Dict[str, Any]:
        """Get token statistics."""
        with self.lock:
            total_tokens = len(self.tokens)
            active_tokens = sum(1 for t in self.tokens.values() if not t.is_revoked)
            expired_tokens = sum(1 for t in self.tokens.values() if datetime.now() > t.expires_at)
            
            # Count by type
            type_counts = {}
            for token_info in self.tokens.values():
                token_type = token_info.token_type.value
                type_counts[token_type] = type_counts.get(token_type, 0) + 1
        
        return {
            "total_tokens": total_tokens,
            "active_tokens": active_tokens,
            "revoked_tokens": total_tokens - active_tokens,
            "expired_tokens": expired_tokens,
            "tokens_by_type": type_counts
        }
    
    def _create_token(self, user_id: str, client_id: str, scope: List[str], 
                     token_type: TokenType, lifetime: timedelta) -> str:
        """Create JWT token with specified parameters."""
        token_id = self._generate_token_id()
        now = datetime.now()
        expires_at = now + lifetime
        
        # Create claims
        claims = TokenClaims(
            iss=self.issuer,
            sub=user_id,
            aud=client_id,
            exp=int(expires_at.timestamp()),
            iat=int(now.timestamp()),
            jti=token_id,
            typ=token_type.value,
            scope=scope
        )
        
        # Create JWT
        token = self._encode_jwt(claims.to_dict())
        
        # Store token info
        with self.lock:
            self.tokens[token_id] = TokenInfo(
                token_id=token_id,
                user_id=user_id,
                client_id=client_id,
                token_type=token_type,
                scope=scope,
                created_at=now,
                expires_at=expires_at
            )
        
        logger.info(f"Created {token_type.value} token for user {user_id}, client {client_id}")
        return token
    
    def _encode_jwt(self, payload: Dict[str, Any]) -> str:
        """Encode JWT token."""
        # Create header
        header = JWTHeader()
        
        # Encode header and payload
        header_encoded = self._base64url_encode(json.dumps(header.to_dict()).encode())
        payload_encoded = self._base64url_encode(json.dumps(payload).encode())
        
        # Create signature
        message = f"{header_encoded}.{payload_encoded}"
        signature = self._create_signature(message)
        
        return f"{message}.{signature}"
    
    def _decode_jwt(self, token: str) -> Dict[str, Any]:
        """Decode and verify JWT token."""
        try:
            parts = token.split('.')
            if len(parts) != 3:
                raise ValueError("Invalid JWT format")
            
            header_encoded, payload_encoded, signature = parts
            
            # Verify signature
            message = f"{header_encoded}.{payload_encoded}"
            expected_signature = self._create_signature(message)
            
            if not hmac.compare_digest(signature, expected_signature):
                raise ValueError("Invalid JWT signature")
            
            # Decode payload
            payload_bytes = self._base64url_decode(payload_encoded)
            payload = json.loads(payload_bytes.decode())
            
            return payload
            
        except Exception as e:
            raise ValueError(f"JWT decode failed: {e}")
    
    def _create_signature(self, message: str) -> str:
        """Create HMAC signature for JWT."""
        signature = hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).digest()
        return self._base64url_encode(signature)
    
    def _base64url_encode(self, data: bytes) -> str:
        """Base64URL encode."""
        return base64.urlsafe_b64encode(data).decode().rstrip('=')
    
    def _base64url_decode(self, data: str) -> bytes:
        """Base64URL decode."""
        # Add padding if needed
        padding = 4 - len(data) % 4
        if padding != 4:
            data += '=' * padding
        return base64.urlsafe_b64decode(data)
    
    def _generate_secret_key(self) -> str:
        """Generate secret key for JWT signing."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode()
    
    def _generate_token_id(self) -> str:
        """Generate unique token ID."""
        return secrets.token_urlsafe(16)


# Global token manager instance
token_manager = TokenManager()
