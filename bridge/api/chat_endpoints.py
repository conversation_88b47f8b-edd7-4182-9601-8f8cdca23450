"""
API endpoints for multi-turn chat functionality.
Provides RESTful and WebSocket endpoints for conversational interface.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify, Response
from flask_socketio import SocketIO, emit, disconnect
from dataclasses import asdict

from bridge.integrations.chat_manager import chat_manager, MessageRole, ChatContext, ChatStatus
from bridge.core.conversation_context import conversation_context
from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for chat endpoints
chat_bp = Blueprint('chat', __name__, url_prefix='/api/chat')


@chat_bp.route('/sessions', methods=['POST'])
@session_auth.require_auth(['write'])
def create_chat_session():
    """
    Create a new chat session.
    
    Expected JSON payload:
    {
        "title": "Optional session title",
        "context": {
            "file_path": "/path/to/file.py",
            "project_root": "/path/to/project",
            "language": "python"
        }
    }
    """
    try:
        data = request.get_json() or {}
        
        title = data.get('title')
        context_data = data.get('context', {})
        
        # Create chat context
        context = ChatContext(
            file_path=context_data.get('file_path'),
            project_root=context_data.get('project_root'),
            language=context_data.get('language'),
            current_selection=context_data.get('selection'),
            cursor_position=context_data.get('cursor_position')
        )
        
        # Create session
        session_id = chat_manager.create_session(title, context)
        
        return jsonify({
            "status": "success",
            "session_id": session_id,
            "message": "Chat session created successfully"
        })
        
    except Exception as e:
        logger.error(f"Error creating chat session: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions', methods=['GET'])
@session_auth.require_auth(['read'])
def list_chat_sessions():
    """List all chat sessions."""
    try:
        status_filter = request.args.get('status')
        status_enum = None
        
        if status_filter:
            try:
                status_enum = ChatStatus(status_filter)
            except ValueError:
                return jsonify({"error": f"Invalid status: {status_filter}"}), 400
        
        sessions = chat_manager.list_sessions(status_enum)
        
        return jsonify({
            "status": "success",
            "sessions": [session.to_dict() for session in sessions]
        })
        
    except Exception as e:
        logger.error(f"Error listing chat sessions: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>', methods=['GET'])
def get_chat_session(session_id: str):
    """Get a specific chat session."""
    try:
        session = chat_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify({
            "status": "success",
            "session": session.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Error getting chat session: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>/messages', methods=['POST'])
def send_message(session_id: str):
    """
    Send a message to a chat session.
    
    Expected JSON payload:
    {
        "message": "User message content",
        "context": {
            "file_path": "/path/to/file.py",
            "content": "file content",
            "cursor_line": 10,
            "cursor_column": 5,
            "selection": "selected code",
            "language": "python"
        }
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        message = data.get('message')
        if not message:
            return jsonify({"error": "No message provided"}), 400
        
        context = data.get('context', {})
        
        # Update conversation context
        conversation_context.update_context_from_message(session_id, message, context)
        
        # Send message and get response
        async def get_response():
            response_chunks = []
            async for chunk in chat_manager.send_message(session_id, message, context):
                response_chunks.append(chunk)
            return "".join(response_chunks)
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(get_response())
        finally:
            loop.close()
        
        return jsonify({
            "status": "success",
            "response": response,
            "session_id": session_id
        })
        
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>/stream', methods=['POST'])
def stream_message(session_id: str):
    """
    Send a message and stream the response.
    
    Returns a streaming response with Server-Sent Events.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        message = data.get('message')
        if not message:
            return jsonify({"error": "No message provided"}), 400
        
        context = data.get('context', {})
        
        # Update conversation context
        conversation_context.update_context_from_message(session_id, message, context)
        
        def generate_response():
            """Generator for streaming response."""
            async def stream_async():
                async for chunk in chat_manager.send_message(session_id, message, context):
                    yield f"data: {json.dumps({'chunk': chunk})}\n\n"
                yield f"data: {json.dumps({'done': True})}\n\n"
            
            # Run async generator
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = stream_async()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(
            generate_response(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )
        
    except Exception as e:
        logger.error(f"Error streaming message: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>/context', methods=['PUT'])
def update_session_context(session_id: str):
    """
    Update the context for a chat session.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "project_root": "/path/to/project",
        "current_selection": "selected code",
        "cursor_position": {"line": 10, "column": 5},
        "language": "python"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Create chat context
        context = ChatContext(
            file_path=data.get('file_path'),
            project_root=data.get('project_root'),
            current_selection=data.get('current_selection'),
            cursor_position=data.get('cursor_position'),
            language=data.get('language'),
            symbols_in_scope=data.get('symbols_in_scope'),
            recent_changes=data.get('recent_changes')
        )
        
        # Update session context
        success = chat_manager.update_context(session_id, context)
        
        if success:
            return jsonify({
                "status": "success",
                "message": "Context updated successfully"
            })
        else:
            return jsonify({"error": "Session not found"}), 404
        
    except Exception as e:
        logger.error(f"Error updating session context: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>/summary', methods=['GET'])
def get_session_summary(session_id: str):
    """Get a summary of a chat session."""
    try:
        summary = chat_manager.get_session_summary(session_id)
        if not summary:
            return jsonify({"error": "Session not found"}), 404
        
        # Add context summary
        context_summary = conversation_context.get_context_summary(session_id)
        context_stats = conversation_context.get_context_stats(session_id)
        
        summary['context_summary'] = context_summary
        summary['context_stats'] = context_stats
        
        return jsonify({
            "status": "success",
            "summary": summary
        })
        
    except Exception as e:
        logger.error(f"Error getting session summary: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>', methods=['DELETE'])
def delete_chat_session(session_id: str):
    """Delete a chat session."""
    try:
        success = chat_manager.delete_session(session_id)
        
        if success:
            # Also clear conversation context
            conversation_context.clear_session_context(session_id)
            
            return jsonify({
                "status": "success",
                "message": "Session deleted successfully"
            })
        else:
            return jsonify({"error": "Session not found"}), 404
        
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        return jsonify({"error": str(e)}), 500


@chat_bp.route('/sessions/<session_id>/clear', methods=['POST'])
def clear_session_messages(session_id: str):
    """Clear all messages from a chat session."""
    try:
        success = chat_manager.clear_session_messages(session_id)
        
        if success:
            # Also clear conversation context
            conversation_context.clear_session_context(session_id)
            
            return jsonify({
                "status": "success",
                "message": "Session messages cleared successfully"
            })
        else:
            return jsonify({"error": "Session not found"}), 404
        
    except Exception as e:
        logger.error(f"Error clearing session messages: {e}")
        return jsonify({"error": str(e)}), 500


# WebSocket events for real-time chat
def setup_chat_websocket(socketio: SocketIO):
    """Set up WebSocket events for real-time chat."""
    
    @socketio.on('chat_join')
    def handle_chat_join(data):
        """Handle client joining a chat session."""
        session_id = data.get('session_id')
        if not session_id:
            emit('chat_error', {"error": "No session_id provided"})
            return
        
        session = chat_manager.get_session(session_id)
        if not session:
            emit('chat_error', {"error": "Session not found"})
            return
        
        # Join room for this session
        from flask_socketio import join_room
        join_room(session_id)
        
        emit('chat_joined', {
            "session_id": session_id,
            "message": "Joined chat session successfully"
        })
        
        logger.info(f"Client joined chat session {session_id}")
    
    @socketio.on('chat_message')
    def handle_chat_message(data):
        """Handle real-time chat message."""
        try:
            session_id = data.get('session_id')
            message = data.get('message')
            context = data.get('context', {})
            
            if not session_id or not message:
                emit('chat_error', {"error": "Missing session_id or message"})
                return
            
            # Update conversation context
            conversation_context.update_context_from_message(session_id, message, context)
            
            # Send message and stream response
            async def stream_response():
                try:
                    response_chunks = []
                    async for chunk in chat_manager.send_message(session_id, message, context):
                        response_chunks.append(chunk)
                        
                        # Emit chunk to room
                        socketio.emit('chat_chunk', {
                            "session_id": session_id,
                            "chunk": chunk
                        }, room=session_id)
                    
                    # Emit completion
                    socketio.emit('chat_complete', {
                        "session_id": session_id,
                        "full_response": "".join(response_chunks)
                    }, room=session_id)
                    
                except Exception as e:
                    logger.error(f"Error in chat stream: {e}")
                    socketio.emit('chat_error', {
                        "session_id": session_id,
                        "error": str(e)
                    }, room=session_id)
            
            # Run async function
            asyncio.create_task(stream_response())
            
        except Exception as e:
            logger.error(f"Error in chat message handler: {e}")
            emit('chat_error', {"error": str(e)})
    
    @socketio.on('chat_leave')
    def handle_chat_leave(data):
        """Handle client leaving a chat session."""
        session_id = data.get('session_id')
        if session_id:
            from flask_socketio import leave_room
            leave_room(session_id)
            emit('chat_left', {"session_id": session_id})
            logger.info(f"Client left chat session {session_id}")


# Health check for chat service
@chat_bp.route('/health', methods=['GET'])
def chat_health():
    """Health check for chat service."""
    try:
        # Get basic stats
        all_sessions = chat_manager.list_sessions()
        active_sessions = [s for s in all_sessions if s.status == ChatStatus.ACTIVE]
        
        return jsonify({
            "status": "healthy",
            "total_sessions": len(all_sessions),
            "active_sessions": len(active_sessions),
            "context_items": len(conversation_context.context_items)
        })
    except Exception as e:
        logger.error(f"Chat health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500
