"""
API endpoints for code completion functionality.
Provides RESTful and WebSocket endpoints for intelligent code completion.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify
from flask_socketio import Socket<PERSON>, emit, disconnect
from dataclasses import asdict

from bridge.integrations.code_completion import (
    completion_engine, 
    CompletionContext, 
    CompletionResponse
)
from bridge.core.context_manager import context_analyzer

logger = logging.getLogger(__name__)

# Create blueprint for completion endpoints
completion_bp = Blueprint('completion', __name__, url_prefix='/api/completion')


@completion_bp.route('/complete', methods=['POST'])
def complete_code():
    """
    RESTful endpoint for code completion.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "cursor_line": 10,
        "cursor_column": 5,
        "language": "python",
        "project_root": "/path/to/project",
        "selection": "optional selected text"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Create completion context
        context = CompletionContext(
            file_path=data['file_path'],
            content=data['content'],
            cursor_line=data['cursor_line'],
            cursor_column=data['cursor_column'],
            language=data.get('language', 'text'),
            project_root=data.get('project_root'),
            selection=data.get('selection'),
            surrounding_context=data.get('surrounding_context')
        )
        
        # Get completions asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(completion_engine.get_completions(context))
        finally:
            loop.close()
        
        # Convert response to JSON
        response_dict = asdict(response)
        
        logger.info(f"Completion request for {context.file_path}:{context.cursor_line}:{context.cursor_column} - {len(response.items)} suggestions")
        
        return jsonify(response_dict)
        
    except Exception as e:
        logger.error(f"Error in completion endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/analyze', methods=['POST'])
def analyze_file():
    """
    Analyze a file for context information.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "language": "python"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'content']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Analyze file
        file_context = context_analyzer.analyze_file(
            file_path=data['file_path'],
            content=data['content'],
            language=data.get('language')
        )
        
        # Convert to JSON-serializable format
        response = {
            "file_path": file_context.file_path,
            "language": file_context.language,
            "symbols": [asdict(symbol) for symbol in file_context.symbols],
            "imports": file_context.imports,
            "dependencies": file_context.dependencies,
            "project_root": file_context.project_root
        }
        
        logger.info(f"File analysis for {file_context.file_path} - {len(file_context.symbols)} symbols found")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in analyze endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/context', methods=['POST'])
def get_context_at_position():
    """
    Get context information at a specific position.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "line": 10,
        "column": 5
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'line', 'column']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Get context at position
        symbol = context_analyzer.get_context_at_position(
            file_path=data['file_path'],
            line=data['line'],
            column=data['column']
        )
        
        if symbol:
            response = asdict(symbol)
        else:
            response = None
        
        return jsonify({"symbol": response})
        
    except Exception as e:
        logger.error(f"Error in context endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/symbols', methods=['POST'])
def get_symbols_in_scope():
    """
    Get symbols visible at a specific line.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "line": 10
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'line']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Get symbols in scope
        symbols = context_analyzer.get_symbols_in_scope(
            file_path=data['file_path'],
            line=data['line']
        )
        
        response = {
            "symbols": [asdict(symbol) for symbol in symbols]
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in symbols endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/cache/clear', methods=['POST'])
def clear_completion_cache():
    """Clear the completion cache."""
    try:
        completion_engine.clear_cache()
        return jsonify({"status": "success", "message": "Completion cache cleared"})
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        return jsonify({"error": str(e)}), 500


# WebSocket events for real-time completion
def setup_completion_websocket(socketio: SocketIO):
    """Set up WebSocket events for real-time completion."""
    
    @socketio.on('completion_request')
    def handle_completion_request(data):
        """Handle real-time completion request via WebSocket."""
        try:
            # Validate data
            required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column']
            for field in required_fields:
                if field not in data:
                    emit('completion_error', {"error": f"Missing required field: {field}"})
                    return
            
            # Create completion context
            context = CompletionContext(
                file_path=data['file_path'],
                content=data['content'],
                cursor_line=data['cursor_line'],
                cursor_column=data['cursor_column'],
                language=data.get('language', 'text'),
                project_root=data.get('project_root'),
                selection=data.get('selection'),
                surrounding_context=data.get('surrounding_context')
            )
            
            # Get completions asynchronously
            async def get_completions_async():
                try:
                    response = await completion_engine.get_completions(context)
                    
                    # Emit response
                    socketio.emit('completion_response', {
                        "context_id": data.get('context_id'),
                        "response": asdict(response)
                    })
                    
                except Exception as e:
                    logger.error(f"Error in async completion: {e}")
                    socketio.emit('completion_error', {
                        "context_id": data.get('context_id'),
                        "error": str(e)
                    })
            
            # Run async function
            asyncio.create_task(get_completions_async())
            
        except Exception as e:
            logger.error(f"Error in WebSocket completion request: {e}")
            emit('completion_error', {"error": str(e)})
    
    @socketio.on('completion_cancel')
    def handle_completion_cancel(data):
        """Handle completion cancellation."""
        context_id = data.get('context_id')
        if context_id:
            # Cancel any ongoing completion for this context
            logger.info(f"Completion cancelled for context {context_id}")
            emit('completion_cancelled', {"context_id": context_id})
    
    @socketio.on('file_analysis_request')
    def handle_file_analysis(data):
        """Handle file analysis request via WebSocket."""
        try:
            # Validate data
            required_fields = ['file_path', 'content']
            for field in required_fields:
                if field not in data:
                    emit('analysis_error', {"error": f"Missing required field: {field}"})
                    return
            
            # Analyze file
            file_context = context_analyzer.analyze_file(
                file_path=data['file_path'],
                content=data['content'],
                language=data.get('language')
            )
            
            # Emit response
            emit('analysis_response', {
                "request_id": data.get('request_id'),
                "file_path": file_context.file_path,
                "language": file_context.language,
                "symbols": [asdict(symbol) for symbol in file_context.symbols],
                "imports": file_context.imports,
                "dependencies": file_context.dependencies
            })
            
        except Exception as e:
            logger.error(f"Error in WebSocket file analysis: {e}")
            emit('analysis_error', {
                "request_id": data.get('request_id'),
                "error": str(e)
            })
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection."""
        logger.info("Completion client connected")
        emit('connected', {"status": "connected"})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection."""
        logger.info("Completion client disconnected")


# Health check for completion service
@completion_bp.route('/health', methods=['GET'])
def completion_health():
    """Health check for completion service."""
    try:
        # Check if completion engine is working
        cache_size = len(completion_engine.cache.cache)
        
        return jsonify({
            "status": "healthy",
            "cache_size": cache_size,
            "active_sessions": len(completion_engine.active_sessions)
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500
